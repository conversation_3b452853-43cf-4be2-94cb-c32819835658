'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { useAppDispatch, useAppSelector } from '@/store';
import { requestVerification } from '@/store/slices/employeeSlice';
import { getProfile } from '@/store/slices/authSlice';

export default function RequestDomainVerificationPage() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'sent' | 'error'>(
    'idle'
  );
  const [error, setError] = useState('');
  const router = useRouter();
  const dispatch = useAppDispatch();
  const user = useAppSelector((state: any) => state.auth.user);
  const profileLoading = useAppSelector((state: any) => state.auth.isLoading);
  const domainStatusData = useAppSelector(
    (state: any) => state.employee.domainVerificationStatus
  );

  useEffect(() => {
    // Ensure user profile is loaded only if we have an auth token
    if (!user && !profileLoading) {
      const token =
        typeof window !== 'undefined'
          ? localStorage.getItem('authToken')
          : null;
      if (token) {
        dispatch(getProfile());
      }
    }
    // If already verified, redirect to dashboard
    if (
      user?.role?.name?.toLowerCase() === 'employee' &&
      domainStatusData?.domainVerified === true
    ) {
      router.replace('/dashboard');
    }
    // If the user is an employee and has an email, prefill it
    if (user?.role.name?.toLowerCase() === 'employee' && user.email) {
      setEmail(user.email);
    }
  }, [user, profileLoading, domainStatusData, dispatch, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus('loading');
    setError('');
    try {
      const data = await dispatch(requestVerification({ email })).unwrap();
      setStatus('sent');
      setError('');
      toast.success(
        data.message || 'Verification email sent! Please check your inbox.'
      );
    } catch (err: any) {
      setError(err?.data?.message || err?.message || 'An error occurred.');
      toast.error(err?.data?.message || err?.message || 'An error occurred.');
      setStatus('error');
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-[#E3F0FF] to-[#B6D0F7] py-8 px-4">
      <div className="bg-white rounded-3xl shadow-2xl flex flex-col items-center w-full max-w-lg p-10">
        <h1 className="text-3xl font-extrabold mb-3 text-[#1976F6]">
          Request Domain Verification
        </h1>
        <p className="mb-8 text-center text-gray-500 max-w-md">
          To access the platform as an employee, you must verify your work email
          domain.
        </p>
        {status === 'sent' ? (
          <div className="flex flex-col items-center w-full">
            <p className="text-green-700 text-lg font-semibold mb-4 text-center">
              {"We've sent a verification link to"}{' '}
              <span className="font-bold">{email}</span>.<br />
              Please check your email inbox and click the link to verify your
              work email domain.
              <br />
              {
                "If you don't see the email, please check your spam or junk folder."
              }
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-full">
            <input
              type="email"
              className="border border-gray-200 rounded-lg px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-[#1976F6]"
              placeholder="<EMAIL>"
              value={email}
              onChange={e => setEmail(e.target.value)}
              required
              readOnly={!!user?.email}
            />
            <button
              type="submit"
              className="bg-[#1976F6] text-white rounded-lg px-4 py-3 font-semibold text-lg hover:bg-blue-700 transition disabled:opacity-50"
              disabled={status === 'loading' || !email}
            >
              {status === 'loading' ? 'Sending...' : 'Send Verification Email'}
            </button>
            {status === 'error' && (
              <p className="mt-2 text-red-600 text-center">{error}</p>
            )}
          </form>
        )}
      </div>
    </div>
  );
}
