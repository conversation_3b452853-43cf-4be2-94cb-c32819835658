'use client';
import React, { useState } from 'react';
import { Di<PERSON>, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Eye } from 'lucide-react';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/store';
import { getEmployerJobs } from '@/store/slices/employeeSlice';
import { getProfile } from '@/store/slices/authSlice';
import { useEffect } from 'react';
import type { Job } from '@/types/employee';

// Helper to extract a user-friendly error message from RTK Query error
function getErrorMessage(error: unknown): string {
  if (!error) return 'Failed to fetch jobs';
  // FetchBaseQueryError with error string
  if (typeof error === 'object' && error !== null) {
    if ('error' in error && typeof (error as any).error === 'string') {
      return (error as any).error;
    }
    // FetchBaseQueryError with data.message
    if (
      'data' in error &&
      typeof (error as any).data === 'object' &&
      (error as any).data !== null &&
      'message' in (error as any).data
    ) {
      return String((error as any).data.message);
    }
    // SerializedError with message
    if ('message' in error && typeof (error as any).message === 'string') {
      return (error as any).message;
    }
  }
  return 'Failed to fetch jobs';
}

export default function PostJobPage() {
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.auth.user);
  const profileLoading = useAppSelector(state => state.auth.isLoading);
  const employerId = user?.id || user?._id;
  useEffect(() => {
    // Ensure profile is loaded only if we have an auth token
    if (!user && !profileLoading) {
      const token =
        typeof window !== 'undefined'
          ? localStorage.getItem('authToken')
          : null;
      if (token) {
        dispatch(getProfile());
      }
    }
    // Once loaded, fetch jobs
    if (employerId) {
      dispatch(getEmployerJobs(employerId));
    }
  }, [dispatch, employerId, user, profileLoading]);
  const employerJobs = useAppSelector(state => state.employee.employerJobs);
  const isLoading = useAppSelector(state => state.employee.loading);
  const error = useAppSelector(state => state.employee.error);
  const data = employerJobs;
  const [viewJob, setViewJob] = useState<Job | null>(null);

  return (
    <div className="bg-gradient-to-br from-[#E3F0FF] to-[#F6F9FF] min-h-screen py-10 px-2">
      <div className="max-w-7xl mx-auto flex flex-col items-center">
        <div className="w-full rounded-3xl bg-white shadow-lg p-8">
          {/* Job List Header with Create Button */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
            <h2 className="text-2xl font-extrabold text-gray-800">
              My Job Posts
            </h2>
            <Link href="/post-job/new">
              <Button className="w-full md:w-auto px-8 py-3 text-base font-semibold rounded-full shadow-md bg-blue-600 hover:bg-blue-700 transition-colors">
                Create Job
              </Button>
            </Link>
          </div>
          {/* Job List Section */}
          <div className="w-full">
            {error ? (
              <div className="text-red-600">{getErrorMessage(error)}</div>
            ) : isLoading ? (
              <div className="text-gray-500">Loading jobs...</div>
            ) : !data?.data || data.data.length === 0 ? (
              <div className="text-gray-500">No jobs posted yet.</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {data.data.map((job: Job) => (
                  <Card
                    key={job._id || job.id}
                    className="rounded-2xl shadow-xl border-0 bg-gradient-to-br from-white to-blue-50 hover:shadow-2xl hover:-translate-y-1 transition-all duration-200"
                  >
                    <CardContent className="p-7 flex flex-col h-full justify-between">
                      <div>
                        <h3 className="font-extrabold text-xl text-blue-800 mb-2 truncate">
                          {job.title}
                        </h3>
                        <div className="text-gray-600 font-medium mb-2">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-semibold text-gray-800">
                              {job.company}
                            </span>
                            {job.remote_ok && (
                              <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-semibold">
                                Remote
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            📍 {job.location}
                          </div>
                          <div className="text-sm text-gray-600">
                            💰 ${job.salary_min?.toLocaleString()} - $
                            {job.salary_max?.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600">
                            Domain:{' '}
                            <span className="font-semibold text-gray-800">
                              {job.domain}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-auto pt-2 border-t border-blue-100 items-center justify-between">
                        <div className="flex flex-wrap gap-2">
                          <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-semibold text-xs">
                            Tech: {job.criteria_weights?.technical_skills}%
                          </span>
                          <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full font-semibold text-xs">
                            Soft: {job.criteria_weights?.soft_skills}%
                          </span>
                          <span className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full font-semibold text-xs">
                            Exp: {job.criteria_weights?.experience}%
                          </span>
                          <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full font-semibold text-xs">
                            Culture: {job.criteria_weights?.cultural_fit}%
                          </span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          className="ml-auto p-2 h-8 w-8 flex items-center justify-center"
                          onClick={() => setViewJob(job)}
                          aria-label="View Details"
                          title="View Details"
                        >
                          <Eye className="h-5 w-5" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
          {/* Job Detail Modal */}
          <Dialog
            open={!!viewJob}
            onOpenChange={open => !open && setViewJob(null)}
          >
            <DialogContent className="max-w-lg w-full">
              {viewJob && (
                <>
                  <DialogHeader>
                    <DialogTitle>{viewJob.title}</DialogTitle>
                    <DialogDescription>Full job details</DialogDescription>
                  </DialogHeader>
                  <div className="mt-4">
                    <div className="mb-3">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-semibold text-gray-800">
                          {viewJob.company}
                        </span>
                        {viewJob.remote_ok && (
                          <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-semibold">
                            Remote
                          </span>
                        )}
                      </div>
                      <div className="text-gray-600 mb-1">
                        📍 {viewJob.location}
                      </div>
                      <div className="text-gray-600 mb-1">
                        💰 ${viewJob.salary_min?.toLocaleString()} - $
                        {viewJob.salary_max?.toLocaleString()}
                      </div>
                      <div className="text-gray-600 font-medium">
                        Domain:{' '}
                        <span className="font-semibold text-gray-800">
                          {viewJob.domain}
                        </span>
                      </div>
                    </div>
                    <div
                      className="mb-4 text-gray-700 whitespace-pre-line overflow-y-auto"
                      style={{ maxHeight: '350px' }}
                    >
                      {viewJob.description}
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-semibold text-xs">
                        Technical Skills:{' '}
                        {viewJob.criteria_weights?.technical_skills}%
                      </span>
                      <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full font-semibold text-xs">
                        Soft Skills: {viewJob.criteria_weights?.soft_skills}%
                      </span>
                      <span className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full font-semibold text-xs">
                        Experience: {viewJob.criteria_weights?.experience}%
                      </span>
                      <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full font-semibold text-xs">
                        Cultural Fit: {viewJob.criteria_weights?.cultural_fit}%
                      </span>
                    </div>
                  </div>
                </>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
